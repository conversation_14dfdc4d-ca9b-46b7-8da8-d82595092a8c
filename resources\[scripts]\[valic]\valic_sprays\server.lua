local QBCore = exports['qb-core']:GetCoreObject()
local sprays = {}
local sprayCounter = 0
local playerSprayCount = {}

-- Function to generate unique spray ID
local function GenerateSprayId()
    sprayCounter = sprayCounter + 1
    return 'spray_' .. sprayCounter .. '_' .. os.time()
end

-- Function to get player spray count
local function GetPlayerSprayCount(playerId)
    return playerSprayCount[playerId] or 0
end

-- Function to increment player spray count
local function IncrementPlayerSprayCount(playerId)
    playerSprayCount[playerId] = GetPlayerSprayCount(playerId) + 1
end

-- Function to decrement player spray count
local function DecrementPlayerSprayCount(playerId)
    local count = GetPlayerSprayCount(playerId)
    if count > 0 then
        playerSprayCount[playerId] = count - 1
    end
end

-- Function to remove old sprays for a player
local function RemoveOldestPlayerSpray(playerId)
    local oldestSpray = nil
    local oldestTime = nil
    
    for sprayId, spray in pairs(sprays) do
        if spray.playerId == playerId then
            if not oldestTime or spray.timestamp < oldestTime then
                oldestTime = spray.timestamp
                oldestSpray = sprayId
            end
        end
    end
    
    if oldestSpray then
        sprays[oldestSpray] = nil
        TriggerClientEvent('valic_sprays:removeSpray', -1, oldestSpray)
        DecrementPlayerSprayCount(playerId)
        return true
    end
    
    return false
end

-- Function to check if player has permission
local function HasPermission(source, permission)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return false end
    
    return QBCore.Functions.HasPermission(source, permission)
end

-- Create spray event
RegisterNetEvent('valic_sprays:createSpray', function(sprayData, itemName)
    local source = source
    local Player = QBCore.Functions.GetPlayer(source)
    
    if not Player then return end
    
    -- Check if player has the item
    local hasItem = exports.ox_inventory:GetItem(source, itemName, nil, true)
    if not hasItem or hasItem < 1 then
        TriggerClientEvent('ox_lib:notify', source, {
            title = 'Sprejování',
            description = 'Nemáš tento spray!',
            type = 'error'
        })
        return
    end
    
    -- Check spray limit per player
    local currentCount = GetPlayerSprayCount(source)
    if currentCount >= Config.MaxSpraysPerPlayer then
        -- Remove oldest spray
        if not RemoveOldestPlayerSpray(source) then
            TriggerClientEvent('ox_lib:notify', source, {
                title = 'Sprejování',
                description = 'Dosáhl jsi limitu sprejů!',
                type = 'error'
            })
            return
        end
    end
    
    -- Remove one spray item
    if not exports.ox_inventory:RemoveItem(source, itemName, 1) then
        TriggerClientEvent('ox_lib:notify', source, {
            title = 'Sprejování',
            description = 'Chyba při odebírání itemu!',
            type = 'error'
        })
        return
    end
    
    -- Generate unique ID
    local sprayId = GenerateSprayId()
    sprayData.id = sprayId
    sprayData.createdAt = os.time()
    
    -- Store spray
    sprays[sprayId] = sprayData
    IncrementPlayerSprayCount(source)
    
    -- Sync to all clients
    TriggerClientEvent('valic_sprays:syncSpray', -1, sprayData)
    
    print(string.format('[valic_sprays] Player %s created spray: "%s" with %s spray', 
        Player.PlayerData.name, sprayData.text, sprayData.colorLabel))
end)

-- Request sprays event (when player joins)
RegisterNetEvent('valic_sprays:requestSprays', function()
    local source = source
    TriggerClientEvent('valic_sprays:syncAllSprays', source, sprays)
end)

-- Clear all sprays (admin only)
RegisterNetEvent('valic_sprays:clearAllSprays', function()
    local source = source
    
    if not HasPermission(source, Config.AdminPermission) then
        TriggerClientEvent('ox_lib:notify', source, {
            title = 'Sprejování',
            description = 'Nemáš oprávnění!',
            type = 'error'
        })
        return
    end
    
    sprays = {}
    playerSprayCount = {}
    TriggerClientEvent('valic_sprays:syncAllSprays', -1, {})
    
    TriggerClientEvent('ox_lib:notify', source, {
        title = 'Sprejování',
        description = 'Všechny spreje byly smazány!',
        type = 'success'
    })
    
    print('[valic_sprays] All sprays cleared by admin')
end)

-- Clear player sprays (admin only)
RegisterNetEvent('valic_sprays:clearPlayerSprays', function(targetPlayerId)
    local source = source
    
    if not HasPermission(source, Config.AdminPermission) then
        TriggerClientEvent('ox_lib:notify', source, {
            title = 'Sprejování',
            description = 'Nemáš oprávnění!',
            type = 'error'
        })
        return
    end
    
    local removedCount = 0
    for sprayId, spray in pairs(sprays) do
        if spray.playerId == targetPlayerId then
            sprays[sprayId] = nil
            TriggerClientEvent('valic_sprays:removeSpray', -1, sprayId)
            removedCount = removedCount + 1
        end
    end
    
    playerSprayCount[targetPlayerId] = 0
    
    TriggerClientEvent('ox_lib:notify', source, {
        title = 'Sprejování',
        description = string.format('Smazáno %d sprejů hráče ID: %d', removedCount, targetPlayerId),
        type = 'success'
    })
    
    print(string.format('[valic_sprays] Cleared %d sprays for player ID: %d', removedCount, targetPlayerId))
end)

-- Cleanup old sprays
if Config.EnableCleanup then
    CreateThread(function()
        while true do
            Wait(60000) -- Check every minute
            
            local currentTime = os.time()
            local cleanupTime = Config.SprayLifetime * 60 -- Convert to seconds
            
            for sprayId, spray in pairs(sprays) do
                if spray.createdAt and (currentTime - spray.createdAt) > cleanupTime then
                    sprays[sprayId] = nil
                    DecrementPlayerSprayCount(spray.playerId)
                    TriggerClientEvent('valic_sprays:removeSpray', -1, sprayId)
                    print(string.format('[valic_sprays] Auto-cleaned expired spray: %s', sprayId))
                end
            end
        end
    end)
end

-- Player disconnect cleanup
AddEventHandler('playerDropped', function()
    local source = source
    playerSprayCount[source] = nil
end)

-- Server startup
AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        print('[valic_sprays] Spray system started successfully!')
        print('[valic_sprays] Available commands: /clearsprays, /clearplayersprays [id]')
    end
end)
