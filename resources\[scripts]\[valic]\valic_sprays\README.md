# Valic Sprays - FiveM QBCore Spray System

Kompletní systém pro sprejování na zdi ve FiveM s QBCore frameworkem.

## Funkce

- **6 barev sprejů**: Červená, modrá, zelená, žlutá, černá, bílá
- **Vlastní text**: <PERSON><PERSON><PERSON><PERSON><PERSON> mohou zadat vlastní text (max 50 znaků)
- **Detekce zdí**: Automatická detekce zdí v dosahu
- **Animace**: Realistická animace sprejování
- **Synchronizace**: Spreje jsou viditelné pro všechny hráče
- **Limity**: Maximálně 10 sprejů na hráče
- **Auto-cleanup**: <PERSON><PERSON><PERSON> ma<PERSON> star<PERSON>ch sprejů (60 minut)
- **Admin příkazy**: Správa sprejů pro administrátory

## Instalace

1. Zkopíruj složku `valic_sprays` do `resources/[scripts]/[valic]/`
2. Přidej do `server.cfg`: `ensure valic_sprays`
3. Přidej itemy do `ox_inventory/data/items.lua` (viz níže)
4. Restartuj server

## Ox_inventory itemy

Přidej tyto itemy do `ox_inventory/data/items.lua`:

```lua
['spray_red'] = {
    label = 'Červený spray',
    weight = 200,
    stack = true,
    close = true,
    description = 'Červený sprej na graffiti',
    client = {
        image = 'spray_red.png',
        export = 'valic_sprays.useSpray'
    }
},

['spray_blue'] = {
    label = 'Modrý spray',
    weight = 200,
    stack = true,
    close = true,
    description = 'Modrý sprej na graffiti',
    client = {
        image = 'spray_blue.png',
        export = 'valic_sprays.useSpray'
    }
},

['spray_green'] = {
    label = 'Zelený spray',
    weight = 200,
    stack = true,
    close = true,
    description = 'Zelený sprej na graffiti',
    client = {
        image = 'spray_green.png',
        export = 'valic_sprays.useSpray'
    }
},

['spray_yellow'] = {
    label = 'Žlutý spray',
    weight = 200,
    stack = true,
    close = true,
    description = 'Žlutý sprej na graffiti',
    client = {
        image = 'spray_yellow.png',
        export = 'valic_sprays.useSpray'
    }
},

['spray_black'] = {
    label = 'Černý spray',
    weight = 200,
    stack = true,
    close = true,
    description = 'Černý sprej na graffiti',
    client = {
        image = 'spray_black.png',
        export = 'valic_sprays.useSpray'
    }
},

['spray_white'] = {
    label = 'Bílý spray',
    weight = 200,
    stack = true,
    close = true,
    description = 'Bílý sprej na graffiti',
    client = {
        image = 'spray_white.png',
        export = 'valic_sprays.useSpray'
    }
},
```

## Použití

1. **Sprejování**: Vezmi spray item z inventáře a použij ho
2. **Detekce zdi**: Postav se blízko zdi (max 2 metry)
3. **Zadání textu**: Zadej text do dialogu (max 50 znaků)
4. **Animace**: Počkej na dokončení animace sprejování

## Admin příkazy

- `/clearsprays` - Smaže všechny spreje (vyžaduje admin oprávnění)
- `/clearplayersprays [player_id]` - Smaže spreje konkrétního hráče

## Konfigurace

Všechna nastavení najdeš v `config.lua`:

- `MaxSprayDistance` - Maximální vzdálenost od zdi (2.0 metry)
- `MaxTextLength` - Maximální délka textu (50 znaků)
- `SprayDuration` - Doba sprejování (3000ms)
- `SprayLifetime` - Doba života sprejů (60 minut)
- `MaxSpraysPerPlayer` - Maximum sprejů na hráče (10)
- `EnableCleanup` - Automatické mazání starých sprejů
- `AdminPermission` - Oprávnění pro admin příkazy

## Závislosti

- `qb-core`
- `ox_lib`
- `ox_inventory`

## Autor

Vytvořil: Valic
Verze: 1.0.0
