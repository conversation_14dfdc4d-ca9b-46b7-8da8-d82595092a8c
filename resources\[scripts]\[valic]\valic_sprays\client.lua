local QBCore = exports['qb-core']:GetCoreObject()
local sprays = {}
local isSprayingActive = false

-- Function to check if player is near a wall
local function IsNearWall()
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    local forwardVector = GetEntityForwardVector(playerPed)
    
    -- Cast a ray forward to detect walls
    local rayHandle = StartExpensiveSynchronousShapeTestLosProbe(
        playerCoords.x, playerCoords.y, playerCoords.z,
        playerCoords.x + forwardVector.x * Config.MaxSprayDistance,
        playerCoords.y + forwardVector.y * Config.MaxSprayDistance,
        playerCoords.z + forwardVector.z * Config.MaxSprayDistance,
        1, -- collision flags for world
        playerPed,
        0
    )
    
    local _, hit, endCoords, _, _ = GetShapeTestResult(rayHandle)
    
    if hit == 1 then
        local distance = #(playerCoords - endCoords)
        return distance <= Config.MaxSprayDistance, endCoords
    end
    
    return false, nil
end

-- Function to get spray text from player
local function GetSprayText()
    local input = lib.inputDialog('Sprejování', {
        {
            type = 'input',
            label = 'Text na zeď',
            description = 'Zadej text, který chceš nasprejovat (max ' .. Config.MaxTextLength .. ' znaků)',
            required = true,
            max = Config.MaxTextLength
        }
    })
    
    if input and input[1] and string.len(input[1]) > 0 then
        return input[1]
    end
    
    return nil
end

-- Function to perform spray animation
local function DoSprayAnimation()
    local playerPed = PlayerPedId()
    
    -- Load animation
    lib.requestAnimDict('switch@franklin@lamar_tagging_wall')
    
    -- Play animation
    TaskPlayAnim(playerPed, 'switch@franklin@lamar_tagging_wall', 'lamar_tagging_wall_loop_lamar', 8.0, -8.0, Config.SprayDuration, 1, 0, false, false, false)
    
    -- Progress bar
    if lib.progressBar({
        duration = Config.SprayDuration,
        label = 'Sprejuješ na zeď...',
        useWhileDead = false,
        canCancel = true,
        disable = {
            car = true,
            move = true,
            combat = true
        }
    }) then
        return true
    else
        ClearPedTasks(playerPed)
        return false
    end
end

-- Function to create spray on wall
local function CreateSpray(sprayData)
    sprays[sprayData.id] = sprayData
end

-- Function to remove spray
local function RemoveSpray(sprayId)
    if sprays[sprayId] then
        sprays[sprayId] = nil
    end
end

-- Main spray function
local function SprayWall(itemName)
    if isSprayingActive then
        lib.notify({
            title = 'Sprejování',
            description = 'Už sprejuješ!',
            type = 'error'
        })
        return
    end
    
    -- Check if near wall
    local nearWall, wallCoords = IsNearWall()
    if not nearWall then
        lib.notify({
            title = 'Sprejování',
            description = 'Musíš být blíž ke zdi!',
            type = 'error'
        })
        return
    end
    
    -- Get spray text
    local sprayText = GetSprayText()
    if not sprayText then
        return
    end
    
    isSprayingActive = true
    
    -- Do spray animation
    if DoSprayAnimation() then
        -- Create spray data
        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)
        local heading = GetEntityHeading(playerPed)
        
        local sprayData = {
            text = sprayText,
            coords = wallCoords,
            heading = heading,
            color = Config.SprayColors[itemName].color,
            colorLabel = Config.SprayColors[itemName].label,
            playerId = GetPlayerServerId(PlayerId()),
            timestamp = GetGameTimer()
        }
        
        -- Send to server
        TriggerServerEvent('valic_sprays:createSpray', sprayData, itemName)
        
        lib.notify({
            title = 'Sprejování',
            description = 'Úspěšně jsi nasprejoval: "' .. sprayText .. '"',
            type = 'success'
        })
    else
        lib.notify({
            title = 'Sprejování',
            description = 'Sprejování bylo zrušeno!',
            type = 'error'
        })
    end
    
    isSprayingActive = false
end

-- Export function for ox_inventory
function useSpray(data, slot)
    local itemName = data.name
    
    if not Config.SprayColors[itemName] then
        lib.notify({
            title = 'Sprejování',
            description = 'Neplatný spray!',
            type = 'error'
        })
        return
    end
    
    SprayWall(itemName)
end

exports('useSpray', useSpray)

-- Render sprays
CreateThread(function()
    while true do
        local playerCoords = GetEntityCoords(PlayerPedId())
        
        for sprayId, spray in pairs(sprays) do
            local distance = #(playerCoords - spray.coords)
            
            if distance <= 50.0 then -- Render distance
                local onScreen, screenX, screenY = GetScreenCoordFromWorldCoord(spray.coords.x, spray.coords.y, spray.coords.z)
                
                if onScreen then
                    SetTextScale(Config.TextScale, Config.TextScale)
                    SetTextFont(Config.Font)
                    SetTextColour(spray.color[1], spray.color[2], spray.color[3], spray.color[4])
                    SetTextCentre(true)
                    SetTextEntry("STRING")
                    AddTextComponentString(spray.text)
                    DrawText(screenX, screenY)
                end
            end
        end
        
        Wait(0)
    end
end)

-- Network events
RegisterNetEvent('valic_sprays:syncSpray', function(sprayData)
    CreateSpray(sprayData)
end)

RegisterNetEvent('valic_sprays:removeSpray', function(sprayId)
    RemoveSpray(sprayId)
end)

RegisterNetEvent('valic_sprays:syncAllSprays', function(allSprays)
    sprays = allSprays
end)

-- Request all sprays when player spawns
AddEventHandler('QBCore:Client:OnPlayerLoaded', function()
    TriggerServerEvent('valic_sprays:requestSprays')
end)

-- Admin command to clear sprays
RegisterCommand('clearsprays', function()
    TriggerServerEvent('valic_sprays:clearAllSprays')
end, false)

-- Admin command to clear player sprays
RegisterCommand('clearplayersprays', function(source, args)
    if args[1] then
        local playerId = tonumber(args[1])
        if playerId then
            TriggerServerEvent('valic_sprays:clearPlayerSprays', playerId)
        end
    end
end, false)
